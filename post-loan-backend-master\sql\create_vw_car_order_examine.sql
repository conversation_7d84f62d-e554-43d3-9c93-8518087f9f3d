-- 删除已存在的视图
DROP VIEW IF EXISTS vw_car_order_examine;

-- 创建找车费用审批视图
-- 注意：这个视图依赖于以下表：car_order_examine, vw_car_order, vw_account_loan, garage_team, garage, car_warehousing
-- 如果某些表不存在，请先创建相应的表或调整视图定义

CREATE VIEW vw_car_order_examine AS
SELECT
  `a`.`id` AS `id`,
  `a`.`transportation_fee` AS `transportation_fee`,
  `a`.`towing_fee` AS `towing_fee`,
  `a`.`tracker_installation_fee` AS `tracker_installation_fee`,
  `a`.`other_reimbursement` AS `other_reimbursement`,
  `a`.`total_money` AS `total_money`,
  `a`.`status` AS `status`,
  `a`.`approve_time` AS `approve_time`,
  `a`.`reasons` AS `reasons`,
  `a`.`create_time` AS `application_time`,
  `a`.`application_by` AS `application_by`,
  `a`.`approve_by` AS `approve_by`,
  `a`.`approve_role` AS `approve_role`,
  `a`.`approval_history` AS `approval_history`,
  `a`.`approval_status` AS `approval_status`,
  COALESCE(`b`.`team_id`, 0) AS `team_id`,
  COALESCE(`b`.`garage_id`, 0) AS `garage_id`,
  `a`.`order_id` AS `order_id`,
  COALESCE(`f`.`library_status`, 0) AS `library_status`,
  `f`.`inbound_time` AS `inbound_time`,
  COALESCE(`b`.`locating_commission`, 0) AS `locating_commission`,
  COALESCE(`b`.`key_status`, 0) AS `key_status`,
  COALESCE(`b`.`collection_method`, 0) AS `collection_method`,
  `b`.`allocation_time` AS `allocation_time`,
  COALESCE(`c`.`customer_name`, '') AS `customer_name`,
  COALESCE(`c`.`mobile_phone`, '') AS `mobile_phone`,
  COALESCE(`c`.`plate_no`, '') AS `plate_no`,
  COALESCE(`c`.`jg_name`, '') AS `jg_name`,
  COALESCE(`c`.`customer_id`, '') AS `customer_id`,
  COALESCE(`c`.`apply_id`, '') AS `apply_id`,
  COALESCE(`d`.`team_name`, '') AS `team_name`,
  COALESCE(`e`.`name`, '') AS `garage_name`
FROM `car_order_examine` `a`
  LEFT JOIN `vw_car_order` `b` ON (`a`.`order_id` = CONVERT(`b`.`id` USING utf8mb4))
  LEFT JOIN `vw_account_loan` `c` ON (CONVERT(`b`.`apply_no` USING utf8mb4) = CONVERT(`c`.`apply_id` USING utf8mb4))
  LEFT JOIN `garage_team` `d` ON (`b`.`team_id` = `d`.`id`)
  LEFT JOIN `garage` `e` ON (`b`.`garage_id` = `e`.`id`)
  LEFT JOIN `car_warehousing` `f` ON (`b`.`apply_no` = `f`.`apply_no`);
