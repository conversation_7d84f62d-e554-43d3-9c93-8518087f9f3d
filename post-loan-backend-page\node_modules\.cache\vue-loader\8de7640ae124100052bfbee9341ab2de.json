{"remainingRequest": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\code_project\\java_project\\loan\\post-loan-backend-page\\src\\views\\vw_car_order_examine\\vw_car_order_examine\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\src\\views\\vw_car_order_examine\\vw_car_order_examine\\index.vue", "mtime": 1754040074745}, {"path": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1753353053918}, {"path": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753353053188}, {"path": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753353054255}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgew0KICB0ZWFtVm1fY2FyX29yZGVyLA0KICBsaXN0VndfY2FyX29yZGVyX2V4YW1pbmUsDQogIGRlbFZ3X2Nhcl9vcmRlcl9leGFtaW5lLA0KICBnZXRDYXJPcmRlckV4YW1pbmVSZWNvcmRzLA0KICBiYXRjaEFwcHJvdmVDYXJPcmRlckV4YW1pbmUsDQogIHNpbmdsZUFwcHJvdmVDYXJPcmRlckV4YW1pbmUNCn0gZnJvbSAnQC9hcGkvdndfY2FyX29yZGVyX2V4YW1pbmUvdndfY2FyX29yZGVyX2V4YW1pbmUnDQppbXBvcnQgdXNlckluZm8gZnJvbSAnQC9sYXlvdXQvY29tcG9uZW50cy9EaWFsb2cvdXNlckluZm8udnVlJw0KaW1wb3J0IGNhckluZm8gZnJvbSAnQC9sYXlvdXQvY29tcG9uZW50cy9EaWFsb2cvY2FySW5mby52dWUnDQoNCmV4cG9ydCBkZWZhdWx0IHsNCiAgbmFtZTogJ1Z3X2Nhcl9vcmRlcl9leGFtaW5lJywNCiAgY29tcG9uZW50czogew0KICAgIHVzZXJJbmZvLA0KICAgIGNhckluZm8sDQogIH0sDQogIGRhdGEoKSB7DQogICAgcmV0dXJuIHsNCiAgICAgIC8vIOmBrue9qeWxgg0KICAgICAgbG9hZGluZzogdHJ1ZSwNCiAgICAgIC8vIOmAieS4reaVsOe7hA0KICAgICAgaWRzOiBbXSwNCiAgICAgIC8vIOmdnuWNleS4quemgeeUqA0KICAgICAgc2luZ2xlOiB0cnVlLA0KICAgICAgLy8g6Z2e5aSa5Liq56aB55SoDQogICAgICBtdWx0aXBsZTogdHJ1ZSwNCiAgICAgIC8vIOaYvuekuuaQnOe0ouadoeS7tg0KICAgICAgc2hvd1NlYXJjaDogdHJ1ZSwNCiAgICAgIC8vIOaAu+adoeaVsA0KICAgICAgdG90YWw6IDAsDQogICAgICAvLyDmib7ovabotLnnlKjlrqHmibnooajmoLzmlbDmja4NCiAgICAgIHZ3X2Nhcl9vcmRlcl9leGFtaW5lTGlzdDogW10sDQogICAgICAvLyDlvLnlh7rlsYLmoIfpopgNCiAgICAgIHRpdGxlOiAnJywNCiAgICAgIC8vIOaYr+WQpuaYvuekuuW8ueWHuuWxgg0KICAgICAgb3BlbjogZmFsc2UsDQogICAgICAvLyDmn6Xor6Llj4LmlbANCiAgICAgIHF1ZXJ5UGFyYW1zOiB7DQogICAgICAgIHBhZ2VOdW06IDEsDQogICAgICAgIHBhZ2VTaXplOiAxNSwNCiAgICAgICAgdGVhbU5hbWU6IG51bGwsDQogICAgICAgIGtleVN0YXR1czogbnVsbCwNCiAgICAgICAgb3JpZ2luYWxseVRpbWU6IG51bGwsDQogICAgICAgIHN0YXJ0VGltZTogJycsDQogICAgICAgIGVuZFRpbWU6ICcnLA0KICAgICAgICBjdXN0b21lck5hbWU6IG51bGwsDQogICAgICAgIHBsYXRlTm86IG51bGwsDQogICAgICAgIGpnTmFtZTogbnVsbCwNCiAgICAgICAgZ2FyYWdlTmFtZTogbnVsbCwNCiAgICAgIH0sDQogICAgICAvLyDooajljZXlj4LmlbANCiAgICAgIGZvcm06IHsNCiAgICAgICAgaWQ6ICcnLA0KICAgICAgICBzdGF0dXM6IDAsDQogICAgICAgIG5ld1N0YXR1czogbnVsbCwNCiAgICAgICAgcmVqZWN0UmVhc29uOiBudWxsLA0KICAgICAgICBjdXN0b21lck5hbWU6ICcnLA0KICAgICAgICBjdXN0b21lcklkOiAnJywNCiAgICAgICAgYXBwbHlJZDogJycsDQogICAgICAgIHBsYXRlTm86ICcnLA0KICAgICAgICBqZ05hbWU6ICcnLA0KICAgICAgICB0ZWFtTmFtZTogJycsDQogICAgICAgIGFsbG9jYXRpb25UaW1lOiAnJywNCiAgICAgICAga2V5U3RhdHVzOiAnJywNCiAgICAgICAgdG90YWxNb25leTogJycsDQogICAgICAgIF9yZWFkb25seTogZmFsc2UsDQogICAgICB9LA0KICAgICAgLy8g6KGo5Y2V5qCh6aqMDQogICAgICBydWxlczogew0KICAgICAgICBpZDogW3sgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICckY29tbWVudOS4jeiDveS4uuepuicsIHRyaWdnZXI6ICdibHVyJyB9XSwNCiAgICAgIH0sDQogICAgICBqZ05hbWVMaXN0OiBbDQogICAgICAgIHsgbGFiZWw6ICdB5YWs5Y+4JywgdmFsdWU6IDEgfSwNCiAgICAgICAgeyBsYWJlbDogJ0Llhazlj7gnLCB2YWx1ZTogMiB9LA0KICAgICAgXSwNCiAgICAgIGtleVN0YXR1c0xpc3Q6IFsNCiAgICAgICAgeyBsYWJlbDogJ+W3sumCruWvhCcsIHZhbHVlOiAxIH0sDQogICAgICAgIHsgbGFiZWw6ICflt7LmlLblm54nLCB2YWx1ZTogMiB9LA0KICAgICAgICB7IGxhYmVsOiAn5bey5b2S6L+YJywgdmFsdWU6IDMgfSwNCiAgICAgIF0sDQogICAgICB0ZWFtTGlzdDogWw0KICAgICAgICB7IGxhYmVsOiAnQeWboumYnycsIHZhbHVlOiAxIH0sDQogICAgICAgIHsgbGFiZWw6ICdC5Zui6ZifJywgdmFsdWU6IDIgfSwNCiAgICAgIF0sDQogICAgICBjdXN0b21lckluZm86IHsgY3VzdG9tZXJJZDogJycsIGFwcGx5SWQ6ICcnIH0sDQogICAgICB1c2VySW5mb1Zpc2libGU6IGZhbHNlLA0KICAgICAgcGxhdGVObzogJycsDQogICAgICBjYXJJbmZvVmlzaWJsZTogZmFsc2UsDQogICAgICAvLyDor6bmg4Xlr7nor53moYbmmL7npLrnirbmgIENCiAgICAgIGRldGFpbHNEaWFsb2dWaXNpYmxlOiBmYWxzZSwNCiAgICAgIC8vIOW9k+WJ<PERSON><PERSON>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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6QA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/vw_car_order_examine/vw_car_order_examine", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"68px\">\r\n      <el-form-item label=\"\" prop=\"customerName\">\r\n        <el-input v-model=\"queryParams.customerName\" placeholder=\"贷款人账户、姓名\" clearable @keyup.enter.native=\"handleQuery\" />\r\n      </el-form-item>\r\n      <el-form-item label=\"\" prop=\"plateNo\">\r\n        <el-input v-model=\"queryParams.plateNo\" placeholder=\"请输入车牌号\" clearable @keyup.enter.native=\"handleQuery\" />\r\n      </el-form-item>\r\n      <el-form-item label=\"\" prop=\"jgName\">\r\n        <el-input v-model=\"queryParams.jgName\" placeholder=\"录入渠道名称\" clearable @keyup.enter.native=\"handleQuery\" />\r\n      </el-form-item>\r\n      <el-form-item label=\"\" prop=\"garageName\">\r\n        <el-input v-model=\"queryParams.garageName\" placeholder=\"请输入车库名称\" clearable @keyup.enter.native=\"handleQuery\" />\r\n      </el-form-item>\r\n      <el-form-item label=\"\" prop=\"keyStatus\">\r\n        <el-select v-model=\"queryParams.keyStatus\" placeholder=\"请选择钥匙状态\" clearable>\r\n          <el-option v-for=\"dict in keyStatusList\" :key=\"dict.value\" :label=\"dict.label\" :value=\"dict.value\" />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"\" prop=\"teamName\">\r\n        <el-input v-model=\"queryParams.teamName\" placeholder=\"找车团队\" clearable @keyup.enter.native=\"handleQuery\" />\r\n      </el-form-item>\r\n      <el-form-item label=\"派单时间\">\r\n        <el-date-picker\r\n          v-model=\"queryParams.originallyTime\"\r\n          style=\"width: 240px\"\r\n          value-format=\"yyyy-MM-dd\"\r\n          type=\"daterange\"\r\n          range-separator=\"-\"\r\n          start-placeholder=\"开始日期\"\r\n          end-placeholder=\"结束日期\"></el-date-picker>\r\n      </el-form-item>\r\n      <el-form-item>\r\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n\r\n    <el-table v-loading=\"loading\" :data=\"vw_car_order_examineList\" @selection-change=\"handleSelectionChange\">\r\n      <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\r\n      <el-table-column label=\"序号\" align=\"center\" type=\"index\" width=\"55\" fixed=\"left\" />\r\n      <el-table-column label=\"贷款人\" align=\"center\" prop=\"customerName\">\r\n        <template slot-scope=\"scope\">\r\n          <el-button\r\n            v-if=\"scope.row.customerId && scope.row.applyId\"\r\n            type=\"text\"\r\n            @click=\"openUserInfo({ customerId: scope.row.customerId, applyId: scope.row.applyId })\">\r\n            {{ scope.row.customerName }}\r\n          </el-button>\r\n          <span v-else>{{ scope.row.customerName }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"联系电话\" align=\"center\" prop=\"mobilePhone\" />\r\n      <!-- 出单渠道 -->\r\n      <el-table-column label=\"出单渠道\" align=\"center\" prop=\"jgName\"></el-table-column>\r\n      <el-table-column label=\"车牌号\" align=\"center\" prop=\"plateNo\">\r\n        <template slot-scope=\"scope\">\r\n          <el-button type=\"text\" @click=\"openCarInfo(scope.row.plateNo)\">{{ scope.row.plateNo }}</el-button>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"接单团队\" align=\"center\" prop=\"teamName\" />\r\n      <el-table-column label=\"派单时间\" align=\"center\" prop=\"allocationTime\" width=\"180\"></el-table-column>\r\n      <el-table-column label=\"钥匙状态\" align=\"center\" prop=\"keyStatus\">\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ scope.row.keyStatus == 1 ? '已邮寄' : scope.row.keyStatus == 2 ? '已收回' : '未归还' }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"入库时间\" align=\"center\" prop=\"inboundTime\" width=\"180\"></el-table-column>\r\n      <el-table-column label=\"找车佣金\" align=\"center\" prop=\"locatingCommission\" />\r\n      <el-table-column label=\"佣金\" align=\"center\" prop=\"transportationFee\" />\r\n      <el-table-column label=\"拖车费\" align=\"center\" prop=\"towingFee\" />\r\n      <el-table-column label=\"贴机费\" align=\"center\" prop=\"trackerInstallationFee\" />\r\n      <el-table-column label=\"其他报销\" align=\"center\" prop=\"otherReimbursement\" />\r\n      <el-table-column label=\"合计费用\" align=\"center\" prop=\"totalMoney\" />\r\n      <el-table-column label=\"审批状态\" align=\"center\" prop=\"status\">\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ getStatusText(scope.row.status) }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\" fixed=\"right\">\r\n        <template slot-scope=\"scope\">\r\n          <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            @click=\"handleViewDetails(scope.row)\"\r\n            v-hasPermi=\"['vw_car_order_examine:vw_car_order_examine:edit']\">\r\n            审批\r\n          </el-button>\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n\r\n    <pagination v-show=\"total > 0\" :total=\"total\" :page.sync=\"queryParams.pageNum\" :limit.sync=\"queryParams.pageSize\" @pagination=\"getList\" />\r\n\r\n    <!-- 费用详情审批对话框 -->\r\n    <el-dialog title=\"找车费用审批详情\" :visible.sync=\"detailsDialogVisible\" width=\"1200px\" append-to-body>\r\n      <!-- 订单基本信息头部 -->\r\n      <div class=\"approval-header\">\r\n        <el-row>\r\n          <el-col :span=\"8\">\r\n            <strong>贷款人：</strong>\r\n            <el-button\r\n              v-if=\"currentOrderInfo && currentOrderInfo.customerId && currentOrderInfo.applyId\"\r\n              type=\"text\"\r\n              @click=\"openUserInfo({ customerId: currentOrderInfo.customerId, applyId: currentOrderInfo.applyId })\"\r\n              style=\"color: #409EFF;\">\r\n              {{ currentOrderInfo.customerName }}\r\n            </el-button>\r\n            <span v-else>{{ currentOrderInfo ? currentOrderInfo.customerName : '' }}</span>\r\n          </el-col>\r\n          <el-col :span=\"8\">\r\n            <strong>接单团队：</strong>{{ currentOrderInfo ? currentOrderInfo.teamName : '' }}\r\n          </el-col>\r\n          <el-col :span=\"8\">\r\n            <strong>车牌号：</strong>\r\n            <el-button\r\n              v-if=\"currentOrderInfo && currentOrderInfo.plateNo\"\r\n              type=\"text\"\r\n              @click=\"openCarInfo(currentOrderInfo.plateNo)\"\r\n              style=\"color: #409EFF;\">\r\n              {{ currentOrderInfo.plateNo }}\r\n            </el-button>\r\n            <span v-else>{{ currentOrderInfo ? currentOrderInfo.plateNo : '' }}</span>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row style=\"margin-top: 10px;\">\r\n          <el-col :span=\"8\">\r\n            <strong>出单渠道：</strong>{{ currentOrderInfo ? currentOrderInfo.jgName : '' }}\r\n          </el-col>\r\n          <el-col :span=\"8\">\r\n            <strong>派单时间：</strong>{{ currentOrderInfo ? currentOrderInfo.allocationTime : '' }}\r\n          </el-col>\r\n          <el-col :span=\"8\">\r\n            <strong>钥匙状态：</strong>\r\n            <span v-if=\"currentOrderInfo\">{{ currentOrderInfo.keyStatus == 1 ? '已邮寄' : currentOrderInfo.keyStatus == 2 ? '已收回' : '未归还' }}</span>\r\n          </el-col>\r\n        </el-row>\r\n      </div>\r\n\r\n      <!-- 批量操作区域 -->\r\n      <div class=\"batch-approval-section\" style=\"margin: 20px 0;\">\r\n        <el-button\r\n          type=\"success\"\r\n          size=\"small\"\r\n          :disabled=\"selectedRecords.length === 0\"\r\n          @click=\"handleBatchApprove('approve')\">\r\n          批量通过 ({{ selectedRecords.length }})\r\n        </el-button>\r\n        <el-button\r\n          type=\"danger\"\r\n          size=\"small\"\r\n          :disabled=\"selectedRecords.length === 0\"\r\n          @click=\"handleBatchApprove('reject')\">\r\n          批量拒绝 ({{ selectedRecords.length }})\r\n        </el-button>\r\n      </div>\r\n\r\n      <el-table\r\n        :data=\"feeRecords\"\r\n        @selection-change=\"handleRecordSelectionChange\"\r\n        v-loading=\"recordsLoading\">\r\n        <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\r\n        <el-table-column label=\"提交时间\" align=\"center\" prop=\"applicationTime\" width=\"150\" />\r\n        <el-table-column label=\"提交人\" align=\"center\" prop=\"applicationBy\" width=\"100\" />\r\n        <el-table-column label=\"佣金\" align=\"center\" prop=\"transportationFee\" width=\"80\" />\r\n        <el-table-column label=\"拖车费\" align=\"center\" prop=\"towingFee\" width=\"80\" />\r\n        <el-table-column label=\"贴机费\" align=\"center\" prop=\"trackerInstallationFee\" width=\"80\" />\r\n        <el-table-column label=\"其他报销\" align=\"center\" prop=\"otherReimbursement\" width=\"80\" />\r\n        <el-table-column label=\"合计费用\" align=\"center\" prop=\"totalMoney\" width=\"100\" />\r\n        <el-table-column label=\"审批状态\" align=\"center\" prop=\"status\" width=\"100\">\r\n          <template slot-scope=\"scope\">\r\n            <el-tag v-if=\"scope.row.status == null || scope.row.status == 0\" type=\"info\">未审核</el-tag>\r\n            <el-tag v-else-if=\"scope.row.status == 1\" type=\"success\">已通过</el-tag>\r\n            <el-tag v-else-if=\"scope.row.status == 7\" type=\"danger\">已拒绝</el-tag>\r\n            <el-tag v-else type=\"warning\">{{ getStatusText(scope.row.status) }}</el-tag>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"审批时间\" align=\"center\" prop=\"approveTime\" width=\"150\" />\r\n        <el-table-column label=\"审批人\" align=\"center\" prop=\"approveBy\" width=\"100\" />\r\n        <el-table-column label=\"拒绝原因\" align=\"center\" prop=\"reasons\" width=\"150\" />\r\n        <el-table-column label=\"操作\" align=\"center\" width=\"150\" fixed=\"right\">\r\n          <template slot-scope=\"scope\">\r\n            <el-button\r\n              size=\"mini\"\r\n              type=\"text\"\r\n              @click=\"handleSingleApprove(scope.row, 'approve')\"\r\n              v-if=\"canApproveRecord(scope.row)\"\r\n            >通过</el-button>\r\n            <el-button\r\n              size=\"mini\"\r\n              type=\"text\"\r\n              @click=\"handleSingleApprove(scope.row, 'reject')\"\r\n              v-if=\"canApproveRecord(scope.row)\"\r\n            >拒绝</el-button>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"detailsDialogVisible = false\">关 闭</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 单个审批确认对话框 -->\r\n    <el-dialog title=\"审批确认\" :visible.sync=\"singleApprovalDialogVisible\" width=\"400px\" append-to-body>\r\n      <el-form ref=\"singleApprovalForm\" :model=\"singleApprovalForm\" label-width=\"80px\">\r\n        <el-form-item label=\"审批结果\">\r\n          <el-tag :type=\"singleApprovalForm.action === 'approve' ? 'success' : 'danger'\">\r\n            {{ singleApprovalForm.action === 'approve' ? '通过' : '拒绝' }}\r\n          </el-tag>\r\n        </el-form-item>\r\n        <el-form-item\r\n          label=\"拒绝原因\"\r\n          prop=\"rejectReason\"\r\n          v-if=\"singleApprovalForm.action === 'reject'\"\r\n          :rules=\"[{ required: true, message: '请输入拒绝原因', trigger: 'blur' }]\">\r\n          <el-input\r\n            v-model=\"singleApprovalForm.rejectReason\"\r\n            type=\"textarea\"\r\n            :rows=\"3\"\r\n            placeholder=\"请输入拒绝原因\"\r\n          ></el-input>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"confirmSingleApproval\">确 定</el-button>\r\n        <el-button @click=\"singleApprovalDialogVisible = false\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 批量审批确认对话框 -->\r\n    <el-dialog title=\"批量审批确认\" :visible.sync=\"batchApprovalDialogVisible\" width=\"400px\" append-to-body>\r\n      <el-form ref=\"batchApprovalForm\" :model=\"batchApprovalForm\" label-width=\"80px\">\r\n        <el-form-item label=\"审批结果\">\r\n          <el-tag :type=\"batchApprovalForm.action === 'approve' ? 'success' : 'danger'\">\r\n            {{ batchApprovalForm.action === 'approve' ? '批量通过' : '批量拒绝' }}\r\n          </el-tag>\r\n        </el-form-item>\r\n        <el-form-item label=\"选中记录\">\r\n          <span>{{ selectedRecords.length }} 条记录</span>\r\n        </el-form-item>\r\n        <el-form-item\r\n          label=\"拒绝原因\"\r\n          prop=\"rejectReason\"\r\n          v-if=\"batchApprovalForm.action === 'reject'\"\r\n          :rules=\"[{ required: true, message: '请输入拒绝原因', trigger: 'blur' }]\">\r\n          <el-input\r\n            v-model=\"batchApprovalForm.rejectReason\"\r\n            type=\"textarea\"\r\n            :rows=\"3\"\r\n            placeholder=\"请输入拒绝原因\"\r\n          ></el-input>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"confirmBatchApproval\">确 定</el-button>\r\n        <el-button @click=\"batchApprovalDialogVisible = false\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 贷款人信息组件 -->\r\n    <userInfo ref=\"userInfo\" :visible.sync=\"userInfoVisible\" title=\"贷款人信息\" :customerInfo=\"customerInfo\" />\r\n    <!-- 车辆信息组件 -->\r\n    <carInfo ref=\"carInfo\" :visible.sync=\"carInfoVisible\" title=\"车辆信息\" :plateNo=\"plateNo\" permission=\"2\" />\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n  teamVm_car_order,\r\n  listVw_car_order_examine,\r\n  delVw_car_order_examine,\r\n  getCarOrderExamineRecords,\r\n  batchApproveCarOrderExamine,\r\n  singleApproveCarOrderExamine\r\n} from '@/api/vw_car_order_examine/vw_car_order_examine'\r\nimport userInfo from '@/layout/components/Dialog/userInfo.vue'\r\nimport carInfo from '@/layout/components/Dialog/carInfo.vue'\r\n\r\nexport default {\r\n  name: 'Vw_car_order_examine',\r\n  components: {\r\n    userInfo,\r\n    carInfo,\r\n  },\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      // 选中数组\r\n      ids: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 总条数\r\n      total: 0,\r\n      // 找车费用审批表格数据\r\n      vw_car_order_examineList: [],\r\n      // 弹出层标题\r\n      title: '',\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 15,\r\n        teamName: null,\r\n        keyStatus: null,\r\n        originallyTime: null,\r\n        startTime: '',\r\n        endTime: '',\r\n        customerName: null,\r\n        plateNo: null,\r\n        jgName: null,\r\n        garageName: null,\r\n      },\r\n      // 表单参数\r\n      form: {\r\n        id: '',\r\n        status: 0,\r\n        newStatus: null,\r\n        rejectReason: null,\r\n        customerName: '',\r\n        customerId: '',\r\n        applyId: '',\r\n        plateNo: '',\r\n        jgName: '',\r\n        teamName: '',\r\n        allocationTime: '',\r\n        keyStatus: '',\r\n        totalMoney: '',\r\n        _readonly: false,\r\n      },\r\n      // 表单校验\r\n      rules: {\r\n        id: [{ required: true, message: '$comment不能为空', trigger: 'blur' }],\r\n      },\r\n      jgNameList: [\r\n        { label: 'A公司', value: 1 },\r\n        { label: 'B公司', value: 2 },\r\n      ],\r\n      keyStatusList: [\r\n        { label: '已邮寄', value: 1 },\r\n        { label: '已收回', value: 2 },\r\n        { label: '已归还', value: 3 },\r\n      ],\r\n      teamList: [\r\n        { label: 'A团队', value: 1 },\r\n        { label: 'B团队', value: 2 },\r\n      ],\r\n      customerInfo: { customerId: '', applyId: '' },\r\n      userInfoVisible: false,\r\n      plateNo: '',\r\n      carInfoVisible: false,\r\n      // 详情对话框显示状态\r\n      detailsDialogVisible: false,\r\n      // 当前订单信息\r\n      currentOrderInfo: null,\r\n      // 费用记录列表\r\n      feeRecords: [],\r\n      // 费用记录加载状态\r\n      recordsLoading: false,\r\n      // 选中的费用记录\r\n      selectedRecords: [],\r\n      // 单个审批对话框\r\n      singleApprovalDialogVisible: false,\r\n      singleApprovalForm: {\r\n        id: '',\r\n        action: '', // 'approve' 或 'reject'\r\n        rejectReason: ''\r\n      },\r\n      // 批量审批对话框\r\n      batchApprovalDialogVisible: false,\r\n      batchApprovalForm: {\r\n        action: '', // 'approve' 或 'reject'\r\n        rejectReason: ''\r\n      },\r\n    }\r\n  },\r\n  created() {\r\n    this.getTeam()\r\n    this.getList()\r\n  },\r\n  methods: {\r\n    // 查询录单渠道、找车团队\r\n    getTeam() {\r\n      teamVm_car_order().then(response => {\r\n        this.teamList = response.team\r\n        this.jgNameList = response.office\r\n      })\r\n    },\r\n    /** 查询找车费用审批列表 */\r\n    getList() {\r\n      this.loading = true\r\n      listVw_car_order_examine(this.queryParams).then(response => {\r\n        this.vw_car_order_examineList = response.rows\r\n        this.total = response.total\r\n        this.loading = false\r\n      })\r\n    },\r\n    // 取消按钮\r\n    cancel() {\r\n      this.open = false\r\n      this.reset()\r\n    },\r\n    // 表单重置\r\n    reset() {\r\n      this.form = {\r\n        id: '',\r\n        status: 0,\r\n        newStatus: null,\r\n        rejectReason: null,\r\n        customerName: '',\r\n        customerId: '',\r\n        applyId: '',\r\n        plateNo: '',\r\n        jgName: '',\r\n        teamName: '',\r\n        allocationTime: '',\r\n        keyStatus: '',\r\n        totalMoney: '',\r\n        _readonly: false,\r\n      }\r\n      this.resetForm('form')\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      if (this.queryParams.originallyTime) {\r\n        this.queryParams.startTime = this.queryParams.originallyTime[0]\r\n        this.queryParams.endTime = this.queryParams.originallyTime[1]\r\n      }\r\n      this.queryParams.pageNum = 1\r\n      this.getList()\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.queryParams.customerName = null\r\n      this.queryParams.plateNo = null\r\n      this.queryParams.jgName = null\r\n      this.queryParams.keyStatus = null\r\n      this.queryParams.teamName = null\r\n      this.queryParams.originallyTime = null\r\n      this.queryParams.startTime = null\r\n      this.queryParams.endTime = null\r\n      this.handleQuery()\r\n    },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map(item => item.id)\r\n      this.single = selection.length !== 1\r\n      this.multiple = !selection.length\r\n    },\r\n    /** 新增按钮操作 */\r\n    handleAdd() {\r\n      this.reset()\r\n      this.open = true\r\n      this.title = '添加找车费用审批'\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      // this.reset()\r\n      this.form.id = row.id\r\n      this.form.rejectReason = row.rejectReason\r\n      this.form.status = row.status\r\n      // const id = row.id || this.ids\r\n      // getVw_car_order_examine(id).then(response => {\r\n      //   // this.form = response.data\r\n\r\n      // })\r\n      this.open = true\r\n      this.title = '找车费用审批'\r\n    },\r\n\r\n    // 获取状态文本\r\n    getStatusText(status) {\r\n      const statusMap = {\r\n        0: '未审批',\r\n        1: '全部通过',\r\n        3: '法诉主管审批',\r\n        4: '总监审批',\r\n        5: '总监抄送',\r\n        6: '总经理/董事长审批(抄送)',\r\n        7: '已拒绝'\r\n      }\r\n      return statusMap[status] || '未知状态'\r\n    },\r\n\r\n\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      const ids = row.id || this.ids\r\n      this.$modal\r\n        .confirm('是否确认删除找车费用审批编号为\"' + ids + '\"的数据项？')\r\n        .then(function () {\r\n          return delVw_car_order_examine(ids)\r\n        })\r\n        .then(() => {\r\n          this.getList()\r\n          this.$modal.msgSuccess('删除成功')\r\n        })\r\n        .catch(() => {})\r\n    },\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      this.download(\r\n        'vw_car_order_examine/vw_car_order_examine/export',\r\n        {\r\n          ...this.queryParams,\r\n        },\r\n        `vw_car_order_examine_${new Date().getTime()}.xlsx`\r\n      )\r\n    },\r\n    openUserInfo(customerInfo) {\r\n      console.log('点击客户信息:', customerInfo)\r\n      if (!customerInfo.customerId || !customerInfo.applyId) {\r\n        this.$modal.msgError('客户信息不完整，无法查看详情')\r\n        return\r\n      }\r\n      this.customerInfo = customerInfo\r\n      this.userInfoVisible = true\r\n    },\r\n    openCarInfo(plateNo) {\r\n      this.plateNo = plateNo\r\n      this.carInfoVisible = true\r\n    },\r\n\r\n    /** 查看费用详情和审批 */\r\n    handleViewDetails(row) {\r\n      this.currentOrderInfo = row\r\n      this.detailsDialogVisible = true\r\n      this.loadFeeRecords(row.orderId)\r\n    },\r\n\r\n    /** 加载费用记录 */\r\n    loadFeeRecords(orderId) {\r\n      this.recordsLoading = true\r\n      getCarOrderExamineRecords(orderId).then(response => {\r\n        this.feeRecords = response.data || []\r\n        this.recordsLoading = false\r\n      }).catch(error => {\r\n        this.recordsLoading = false\r\n        console.error('加载费用记录失败:', error)\r\n        this.$modal.msgError('加载费用记录失败')\r\n      })\r\n    },\r\n\r\n    /** 费用记录选择变化 */\r\n    handleRecordSelectionChange(selection) {\r\n      this.selectedRecords = selection\r\n    },\r\n\r\n    /** 单个审批 */\r\n    handleSingleApprove(record, action) {\r\n      this.singleApprovalForm.id = record.id\r\n      this.singleApprovalForm.action = action\r\n      this.singleApprovalForm.rejectReason = ''\r\n      this.singleApprovalDialogVisible = true\r\n    },\r\n\r\n    /** 确认单个审批 */\r\n    confirmSingleApproval() {\r\n      if (this.singleApprovalForm.action === 'reject') {\r\n        this.$refs[\"singleApprovalForm\"].validate(valid => {\r\n          if (!valid) return\r\n          this.executeSingleApproval()\r\n        })\r\n      } else {\r\n        this.executeSingleApproval()\r\n      }\r\n    },\r\n\r\n    /** 执行单个审批 */\r\n    executeSingleApproval() {\r\n      const data = {\r\n        id: this.singleApprovalForm.id,\r\n        action: this.singleApprovalForm.action,\r\n        rejectReason: this.singleApprovalForm.rejectReason\r\n      }\r\n\r\n      singleApproveCarOrderExamine(data).then(() => {\r\n        this.$modal.msgSuccess(`${this.singleApprovalForm.action === 'approve' ? '通过' : '拒绝'}审批成功`)\r\n        this.singleApprovalDialogVisible = false\r\n        this.loadFeeRecords(this.currentOrderInfo.id)\r\n        this.getList()\r\n      }).catch(() => {\r\n        this.$modal.msgError('审批失败')\r\n      })\r\n    },\r\n\r\n    /** 批量审批 */\r\n    handleBatchApprove(action) {\r\n      if (this.selectedRecords.length === 0) {\r\n        this.$modal.msgError('请选择要审批的记录')\r\n        return\r\n      }\r\n\r\n      this.batchApprovalForm.action = action\r\n      this.batchApprovalForm.rejectReason = ''\r\n      this.batchApprovalDialogVisible = true\r\n    },\r\n\r\n    /** 确认批量审批 */\r\n    confirmBatchApproval() {\r\n      if (this.batchApprovalForm.action === 'reject') {\r\n        this.$refs[\"batchApprovalForm\"].validate(valid => {\r\n          if (!valid) return\r\n          this.executeBatchApproval()\r\n        })\r\n      } else {\r\n        this.executeBatchApproval()\r\n      }\r\n    },\r\n\r\n    /** 执行批量审批 */\r\n    executeBatchApproval() {\r\n      const data = {\r\n        ids: this.selectedRecords.map(record => record.id),\r\n        action: this.batchApprovalForm.action,\r\n        rejectReason: this.batchApprovalForm.rejectReason\r\n      }\r\n\r\n      batchApproveCarOrderExamine(data).then(() => {\r\n        this.$modal.msgSuccess(`批量${this.batchApprovalForm.action === 'approve' ? '通过' : '拒绝'}审批成功`)\r\n        this.batchApprovalDialogVisible = false\r\n        this.loadFeeRecords(this.currentOrderInfo.id)\r\n        this.getList()\r\n      }).catch(() => {\r\n        this.$modal.msgError('批量审批失败')\r\n      })\r\n    },\r\n\r\n    /** 检查记录是否可以审批 */\r\n    canApproveRecord(record) {\r\n      // 这里可以根据记录状态和用户权限判断是否可以审批\r\n      return record.status === 0 // 只有未审批的记录可以审批\r\n    },\r\n\r\n    /** 获取状态标签类型 */\r\n    getStatusTagType(status) {\r\n      switch (status) {\r\n        case 0:\r\n          return 'info'\r\n        case 1:\r\n          return 'success'\r\n        case 7:\r\n          return 'danger'\r\n        case 3:\r\n        case 4:\r\n        case 5:\r\n        case 6:\r\n          return 'warning'\r\n        default:\r\n          return 'info'\r\n      }\r\n    },\r\n  },\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.approval-header {\r\n  background-color: #f5f7fa;\r\n  padding: 15px;\r\n  border-radius: 4px;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.batch-approval-section {\r\n  border: 1px solid #e4e7ed;\r\n  padding: 10px;\r\n  border-radius: 4px;\r\n  background-color: #fafafa;\r\n}\r\n\r\n.el-table {\r\n  margin-top: 10px;\r\n}\r\n\r\n.el-tag {\r\n  margin: 2px;\r\n}\r\n</style>\r\n"]}]}