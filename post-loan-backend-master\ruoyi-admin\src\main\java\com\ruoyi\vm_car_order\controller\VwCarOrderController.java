package com.ruoyi.vm_car_order.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.vm_car_order.domain.VwCarOrder;
import com.ruoyi.car_order_examine.domain.CarOrderExamine;
import com.ruoyi.vm_car_order.service.IVwCarOrderService;
import com.ruoyi.car_order_examine.service.ICarOrderExamineService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.car_team.service.IGarageTeamService;
import com.ruoyi.car_team.domain.GarageTeam;
import com.ruoyi.sys_office.service.ISysOfficeService;
import com.ruoyi.sys_office.domain.SysOffice;
import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * vm_car_orderController
 * 
 * <AUTHOR>
 * @date 2025-06-06
 */
@RestController
@RequestMapping("/vm_car_order/vm_car_order")
public class VwCarOrderController extends BaseController
{
    @Autowired
    private IVwCarOrderService vwCarOrderService;

    @Autowired
    private ICarOrderExamineService carOrderExamineService;

    @Autowired
    private IGarageTeamService garageTeamService;

    @Autowired
    private ISysOfficeService sysOfficeService;

    /**
     * 查询vm_car_order列表
     */
    @PreAuthorize("@ss.hasPermi('vm_car_order:vm_car_order:list')")
    @GetMapping("/list")
    public TableDataInfo list(VwCarOrder vwCarOrder)
    {
        startPage();
        List<VwCarOrder> list = vwCarOrderService.selectVwCarOrderList(vwCarOrder);
        return getDataTable(list);
    }

    /**
     * 查询录单渠道、找车团队
     */
    @GetMapping("/cate")
    public AjaxResult getCate()
    {
        Map<String, Object> result = new HashMap<>();

        // 查询团队列表
        List<GarageTeam> teamList = garageTeamService.selectGarageTeamList(new GarageTeam());
        List<Map<String, Object>> teams = teamList.stream()
            .filter(team -> team.getStatus() == 1) // 只返回启用的团队
            .map(team -> {
                Map<String, Object> teamMap = new HashMap<>();
                teamMap.put("value", team.getId());
                teamMap.put("label", team.getTeamName());
                return teamMap;
            })
            .collect(Collectors.toList());

        // 查询渠道列表
        List<SysOffice> officeList = sysOfficeService.selectSysOfficeList(new SysOffice());
        List<Map<String, Object>> offices = officeList.stream()
            .map(office -> {
                Map<String, Object> officeMap = new HashMap<>();
                officeMap.put("value", office.getId());
                officeMap.put("label", office.getName());
                return officeMap;
            })
            .collect(Collectors.toList());

        result.put("team", teams);
        result.put("office", offices);

        return success(result);
    }

    /**
     * 导出vm_car_order列表
     */
    @PreAuthorize("@ss.hasPermi('vm_car_order:vm_car_order:export')")
    @Log(title = "vm_car_order", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, VwCarOrder vwCarOrder)
    {
        List<VwCarOrder> list = vwCarOrderService.selectVwCarOrderList(vwCarOrder);
        ExcelUtil<VwCarOrder> util = new ExcelUtil<VwCarOrder>(VwCarOrder.class);
        util.exportExcel(response, list, "vm_car_order数据");
    }

    /**
     * 获取vm_car_order详细信息
     */
    @PreAuthorize("@ss.hasPermi('vm_car_order:vm_car_order:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id)
    {
        return success(vwCarOrderService.selectVwCarOrderById(id));
    }

    /**
     * 新增vm_car_order
     */
    @PreAuthorize("@ss.hasPermi('vm_car_order:vm_car_order:add')")
    @Log(title = "vm_car_order", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody VwCarOrder vwCarOrder)
    {
        return toAjax(vwCarOrderService.insertVwCarOrder(vwCarOrder));
    }

    /**
     * 修改vm_car_order
     */
    @PreAuthorize("@ss.hasPermi('vm_car_order:vm_car_order:edit')")
    @Log(title = "vm_car_order", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody VwCarOrder vwCarOrder)
    {
        return toAjax(vwCarOrderService.updateVwCarOrder(vwCarOrder));
    }

    /**
     * 删除vm_car_order
     */
    @PreAuthorize("@ss.hasPermi('vm_car_order:vm_car_order:remove')")
    @Log(title = "vm_car_order", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable String[] ids)
    {
        return toAjax(vwCarOrderService.deleteVwCarOrderByIds(ids));
    }

    /**
     * 提交找车费用
     */
    @PreAuthorize("@ss.hasPermi('vm_car_order:vm_car_order:edit')")
    @Log(title = "提交找车费用", businessType = BusinessType.INSERT)
    @PostMapping("/submitCost")
    public AjaxResult submitCost(@RequestBody CarOrderExamine costData)
    {
        try {
            // 检查该订单ID是否已经提交过费用
            CarOrderExamine existingCost = carOrderExamineService.selectCarOrderExamineByOrderId(costData.getOrderId());
            if (existingCost != null) {
                return error("该订单已提交过找车费用，不能重复提交");
            }

            // 生成新的费用记录ID
            costData.setId(java.util.UUID.randomUUID().toString().replace("-", ""));

            // 设置基本信息
            costData.setStatus(0); // 待审核状态
            costData.setCreateBy(getUsername());
            costData.setCreateTime(new java.util.Date());
            costData.setApplicationTime(new java.util.Date());
            costData.setApplicationBy(getUsername());

            // 计算总费用
            java.math.BigDecimal transportationFee = costData.getTransportationFee() != null ? costData.getTransportationFee() : java.math.BigDecimal.ZERO;
            java.math.BigDecimal towingFee = costData.getTowingFee() != null ? costData.getTowingFee() : java.math.BigDecimal.ZERO;
            java.math.BigDecimal trackerInstallationFee = costData.getTrackerInstallationFee() != null ? costData.getTrackerInstallationFee() : java.math.BigDecimal.ZERO;
            java.math.BigDecimal otherReimbursement = costData.getOtherReimbursement() != null ? costData.getOtherReimbursement() : java.math.BigDecimal.ZERO;

            java.math.BigDecimal totalCost = transportationFee.add(towingFee).add(trackerInstallationFee).add(otherReimbursement);
            costData.setTotalMoney(totalCost);

            // 插入新的费用记录
            return toAjax(carOrderExamineService.insertCarOrderExamine(costData));
        } catch (Exception e) {
            return error(e.getMessage());
        }
    }

    /**
     * 查询找车费用列表
     */
    @PreAuthorize("@ss.hasPermi('vm_car_order:vm_car_order:list')")
    @GetMapping("/costList")
    public TableDataInfo costList(CarOrderExamine carOrderExamine)
    {
        startPage();
        List<CarOrderExamine> list = carOrderExamineService.selectCarOrderExamineList(carOrderExamine);
        return getDataTable(list);
    }

    /**
     * 根据订单ID查询找车费用
     */
    @PreAuthorize("@ss.hasPermi('vm_car_order:vm_car_order:query')")
    @GetMapping("/cost/{orderId}")
    public AjaxResult getCostByOrderId(@PathVariable("orderId") String orderId)
    {
        CarOrderExamine cost = carOrderExamineService.selectCarOrderExamineById(orderId);
        return success(cost);
    }

    /**
     * 邮寄钥匙
     */
    @PreAuthorize("@ss.hasPermi('vm_car_order:vm_car_order:edit')")
    @Log(title = "邮寄钥匙", businessType = BusinessType.UPDATE)
    @PostMapping("/mailKey")
    public AjaxResult mailKey(@RequestBody VwCarOrder carOrder)
    {
        try {
            // 设置钥匙状态为已邮寄(1)
            carOrder.setKeyStatus(1L);
            // 设置钥匙邮寄时间为当前时间
            carOrder.setKeyTime(new java.util.Date());
            // 设置更新信息
            carOrder.setUpdateBy(getUsername());
            carOrder.setUpdateTime(new java.util.Date());

            return toAjax(vwCarOrderService.updateVwCarOrder(carOrder));
        } catch (Exception e) {
            return error("邮寄钥匙失败：" + e.getMessage());
        }
    }
}
